from flask import Blueprint, request, jsonify
from models import db, Transaction, TransactionEntry, Account
from sqlalchemy import or_, desc
from datetime import datetime, date

transactions_bp = Blueprint('transactions', __name__)

@transactions_bp.route('/', methods=['GET'])
def get_transactions():
    """الحصول على قائمة المعاملات"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        posted_only = request.args.get('posted_only', 'false').lower() == 'true'
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        query = Transaction.query
        
        # تطبيق الفلاتر
        if search:
            query = query.filter(or_(
                Transaction.transaction_number.contains(search),
                Transaction.description.contains(search),
                Transaction.reference.contains(search)
            ))
        
        if posted_only:
            query = query.filter(Transaction.is_posted == True)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Transaction.date >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(Transaction.date <= date_to_obj)
            except ValueError:
                pass
        
        # ترتيب النتائج
        query = query.order_by(desc(Transaction.date), desc(Transaction.id))
        
        # تطبيق التصفح
        transactions = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'transactions': [transaction.to_dict() for transaction in transactions.items],
            'total': transactions.total,
            'pages': transactions.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/<int:transaction_id>', methods=['GET'])
def get_transaction(transaction_id):
    """الحصول على معاملة محددة"""
    try:
        transaction = Transaction.query.get_or_404(transaction_id)
        return jsonify(transaction.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/', methods=['POST'])
def create_transaction():
    """إنشاء معاملة جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['description', 'entries']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        if not data['entries'] or len(data['entries']) < 2:
            return jsonify({'error': 'يجب أن تحتوي المعاملة على بندين على الأقل'}), 400
        
        # إنشاء رقم المعاملة
        transaction_number = data.get('transaction_number')
        if not transaction_number:
            # إنشاء رقم تلقائي
            last_transaction = Transaction.query.order_by(desc(Transaction.id)).first()
            next_number = (last_transaction.id + 1) if last_transaction else 1
            transaction_number = f"JE{next_number:06d}"
        
        # التحقق من عدم تكرار رقم المعاملة
        if Transaction.query.filter_by(transaction_number=transaction_number).first():
            return jsonify({'error': 'رقم المعاملة موجود مسبقاً'}), 400
        
        # تحويل التاريخ
        transaction_date = date.today()
        if 'date' in data and data['date']:
            try:
                transaction_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'تنسيق التاريخ غير صحيح'}), 400
        
        # حساب إجمالي المدين والدائن
        total_debit = sum(float(entry.get('debit_amount', 0)) for entry in data['entries'])
        total_credit = sum(float(entry.get('credit_amount', 0)) for entry in data['entries'])
        
        if abs(total_debit - total_credit) > 0.01:
            return jsonify({'error': 'القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن'}), 400
        
        # إنشاء المعاملة
        transaction = Transaction(
            transaction_number=transaction_number,
            date=transaction_date,
            description=data['description'],
            reference=data.get('reference'),
            total_amount=total_debit,
            created_by=data.get('created_by', 'النظام')
        )
        
        db.session.add(transaction)
        db.session.flush()  # للحصول على ID
        
        # إنشاء بنود المعاملة
        for entry_data in data['entries']:
            if 'account_id' not in entry_data:
                db.session.rollback()
                return jsonify({'error': 'معرف الحساب مطلوب لكل بند'}), 400
            
            # التحقق من وجود الحساب
            account = Account.query.get(entry_data['account_id'])
            if not account:
                db.session.rollback()
                return jsonify({'error': f'الحساب {entry_data["account_id"]} غير موجود'}), 400
            
            entry = TransactionEntry(
                transaction_id=transaction.id,
                account_id=entry_data['account_id'],
                debit_amount=entry_data.get('debit_amount', 0),
                credit_amount=entry_data.get('credit_amount', 0),
                description=entry_data.get('description')
            )
            db.session.add(entry)
        
        db.session.commit()
        
        return jsonify(transaction.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/<int:transaction_id>', methods=['PUT'])
def update_transaction(transaction_id):
    """تحديث معاملة موجودة"""
    try:
        transaction = Transaction.query.get_or_404(transaction_id)
        
        if transaction.is_posted:
            return jsonify({'error': 'لا يمكن تعديل معاملة مرحلة'}), 400
        
        data = request.get_json()
        
        # تحديث البيانات الأساسية
        if 'description' in data:
            transaction.description = data['description']
        if 'reference' in data:
            transaction.reference = data['reference']
        if 'date' in data and data['date']:
            try:
                transaction.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'تنسيق التاريخ غير صحيح'}), 400
        
        # تحديث البنود إذا تم تمريرها
        if 'entries' in data:
            # حذف البنود القديمة
            TransactionEntry.query.filter_by(transaction_id=transaction_id).delete()
            
            # حساب إجمالي المدين والدائن
            total_debit = sum(float(entry.get('debit_amount', 0)) for entry in data['entries'])
            total_credit = sum(float(entry.get('credit_amount', 0)) for entry in data['entries'])
            
            if abs(total_debit - total_credit) > 0.01:
                return jsonify({'error': 'القيد غير متوازن'}), 400
            
            # إنشاء البنود الجديدة
            for entry_data in data['entries']:
                entry = TransactionEntry(
                    transaction_id=transaction_id,
                    account_id=entry_data['account_id'],
                    debit_amount=entry_data.get('debit_amount', 0),
                    credit_amount=entry_data.get('credit_amount', 0),
                    description=entry_data.get('description')
                )
                db.session.add(entry)
            
            transaction.total_amount = total_debit
        
        db.session.commit()
        return jsonify(transaction.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/<int:transaction_id>', methods=['DELETE'])
def delete_transaction(transaction_id):
    """حذف معاملة"""
    try:
        transaction = Transaction.query.get_or_404(transaction_id)
        
        if transaction.is_posted:
            return jsonify({'error': 'لا يمكن حذف معاملة مرحلة'}), 400
        
        db.session.delete(transaction)
        db.session.commit()
        
        return jsonify({'message': 'تم حذف المعاملة بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/<int:transaction_id>/post', methods=['POST'])
def post_transaction(transaction_id):
    """ترحيل معاملة"""
    try:
        transaction = Transaction.query.get_or_404(transaction_id)
        
        if transaction.post():
            db.session.commit()
            return jsonify({'message': 'تم ترحيل المعاملة بنجاح'})
        else:
            return jsonify({'error': 'لا يمكن ترحيل المعاملة - تأكد من توازن القيد'}), 400
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@transactions_bp.route('/<int:transaction_id>/unpost', methods=['POST'])
def unpost_transaction(transaction_id):
    """إلغاء ترحيل معاملة"""
    try:
        transaction = Transaction.query.get_or_404(transaction_id)
        
        if transaction.unpost():
            db.session.commit()
            return jsonify({'message': 'تم إلغاء ترحيل المعاملة بنجاح'})
        else:
            return jsonify({'error': 'المعاملة غير مرحلة'}), 400
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
