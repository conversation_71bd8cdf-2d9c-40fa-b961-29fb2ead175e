// وظائف JavaScript مشتركة لنظام المحاسبة

// إعدادات عامة
const API_BASE_URL = '/api';

// وظائف مساعدة للتنسيق
function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount || 0);
}

function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number || 0);
}

function formatDate(dateString, options = {}) {
    if (!dateString) return '-';
    
    const defaultOptions = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    return new Date(dateString).toLocaleDateString('ar-SA', formatOptions);
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    return new Date(dateString).toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// وظائف للتعامل مع API
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    const config = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(API_BASE_URL + url, config);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'حدث خطأ في الطلب');
        }
        
        return data;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

// وظائف للتعامل مع النماذج
function showLoading(element, text = 'جاري التحميل...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                ${text}
            </div>
        `;
    }
}

function showError(element, message) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

function showSuccess(message, duration = 3000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

function showErrorMessage(message, duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="bi bi-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

// وظائف للتعامل مع الجداول
function createDataTable(tableId, options = {}) {
    const defaultOptions = {
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[0, 'desc']]
    };
    
    const config = { ...defaultOptions, ...options };
    
    if ($.fn.DataTable) {
        return $(tableId).DataTable(config);
    }
}

// وظائف للتعامل مع النماذج
function resetForm(formId) {
    const form = document.querySelector(formId);
    if (form) {
        form.reset();
        // إزالة رسائل الخطأ
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });
    }
}

function validateForm(formId, rules) {
    const form = document.querySelector(formId);
    if (!form) return false;
    
    let isValid = true;
    
    // إزالة رسائل الخطأ السابقة
    form.querySelectorAll('.is-invalid').forEach(el => {
        el.classList.remove('is-invalid');
    });
    form.querySelectorAll('.invalid-feedback').forEach(el => {
        el.remove();
    });
    
    // التحقق من القواعد
    Object.keys(rules).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        const rule = rules[fieldName];
        
        if (field && rule.required && !field.value.trim()) {
            showFieldError(field, rule.message || `${fieldName} مطلوب`);
            isValid = false;
        }
    });
    
    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// وظائف للتعامل مع المودال
function showModal(modalId, data = {}) {
    const modal = document.querySelector(modalId);
    if (modal) {
        // تعبئة البيانات إذا تم تمريرها
        Object.keys(data).forEach(key => {
            const field = modal.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = data[key] || '';
            }
        });
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        return bsModal;
    }
}

function hideModal(modalId) {
    const modal = document.querySelector(modalId);
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

// وظائف للتعامل مع البحث
function setupSearch(searchInputId, tableId, searchUrl) {
    const searchInput = document.querySelector(searchInputId);
    const table = document.querySelector(tableId);
    
    if (searchInput && table) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value, table, searchUrl);
            }, 500);
        });
    }
}

async function performSearch(query, tableElement, searchUrl) {
    try {
        showLoading(tableElement.querySelector('tbody'));
        
        const params = new URLSearchParams({ search: query });
        const data = await apiRequest(`${searchUrl}?${params}`);
        
        updateTable(tableElement, data);
    } catch (error) {
        showError(tableElement.querySelector('tbody'), error.message);
    }
}

function updateTable(tableElement, data) {
    const tbody = tableElement.querySelector('tbody');
    if (tbody && data.items) {
        tbody.innerHTML = data.items.map(item => createTableRow(item)).join('');
    }
}

// وظائف للتعامل مع التصفح
function setupPagination(containerId, totalPages, currentPage, onPageChange) {
    const container = document.querySelector(containerId);
    if (!container) return;
    
    let paginationHtml = '<nav><ul class="pagination justify-content-center">';
    
    // زر السابق
    if (currentPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage - 1}">السابق</a></li>`;
    }
    
    // أرقام الصفحات
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
    }
    
    // زر التالي
    if (currentPage < totalPages) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage + 1}">التالي</a></li>`;
    }
    
    paginationHtml += '</ul></nav>';
    container.innerHTML = paginationHtml;
    
    // إضافة مستمعي الأحداث
    container.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && onPageChange) {
                onPageChange(page);
            }
        });
    });
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});
