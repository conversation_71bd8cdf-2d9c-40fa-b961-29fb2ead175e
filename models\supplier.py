from . import db
from datetime import datetime

class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    country = db.Column(db.String(50))
    postal_code = db.Column(db.String(10))
    tax_number = db.Column(db.String(50))  # الرقم الضريبي
    payment_terms = db.Column(db.Integer, default=30)  # شروط الدفع بالأيام
    bank_name = db.Column(db.String(100))  # اسم البنك
    bank_account = db.Column(db.String(50))  # رقم الحساب البنكي
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    # purchase_invoices = db.relationship('PurchaseInvoice', backref='supplier', lazy='dynamic')
    
    def __repr__(self):
        return f'<Supplier {self.code}: {self.name}>'
    
    @property
    def balance(self):
        """حساب رصيد المورد"""
        # حساب إجمالي فواتير الشراء
        # total_purchases = sum(invoice.total_amount for invoice in self.purchase_invoices if invoice.is_posted)
        # حساب إجمالي المدفوعات
        total_payments = 0  # TODO: حساب المدفوعات الفعلية
        return 0 - total_payments  # TODO: إضافة فواتير الشراء
    
    def to_dict(self):
        """تحويل المورد إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'address': self.address,
            'city': self.city,
            'country': self.country,
            'postal_code': self.postal_code,
            'tax_number': self.tax_number,
            'payment_terms': self.payment_terms,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'is_active': self.is_active,
            'notes': self.notes,
            'balance': float(self.balance),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
