from . import db
from datetime import datetime

class Transaction(db.Model):
    """نموذج المعاملات المحاسبية (القيود)"""
    __tablename__ = 'transactions'
    
    id = db.Column(db.Integer, primary_key=True)
    transaction_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    description = db.Column(db.Text, nullable=False)
    reference = db.Column(db.String(100))  # مرجع خارجي (رقم فاتورة، إيصال، إلخ)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    is_posted = db.Column(db.Boolean, default=False)  # هل تم ترحيل القيد
    created_by = db.Column(db.String(100))  # المستخدم الذي أنشأ القيد
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    posted_at = db.Column(db.DateTime)  # تاريخ الترحيل
    
    # العلاقات
    entries = db.relationship('TransactionEntry', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Transaction {self.transaction_number}: {self.description}>'
    
    @property
    def total_debit(self):
        """إجمالي المدين"""
        return sum(entry.debit_amount for entry in self.entries if entry.debit_amount)
    
    @property
    def total_credit(self):
        """إجمالي الدائن"""
        return sum(entry.credit_amount for entry in self.entries if entry.credit_amount)
    
    @property
    def is_balanced(self):
        """التحقق من توازن القيد"""
        return abs(self.total_debit - self.total_credit) < 0.01
    
    def post(self):
        """ترحيل القيد"""
        if self.is_balanced and not self.is_posted:
            self.is_posted = True
            self.posted_at = datetime.utcnow()
            return True
        return False
    
    def unpost(self):
        """إلغاء ترحيل القيد"""
        if self.is_posted:
            self.is_posted = False
            self.posted_at = None
            return True
        return False
    
    def to_dict(self):
        """تحويل المعاملة إلى قاموس"""
        return {
            'id': self.id,
            'transaction_number': self.transaction_number,
            'date': self.date.isoformat() if self.date else None,
            'description': self.description,
            'reference': self.reference,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'is_posted': self.is_posted,
            'created_by': self.created_by,
            'total_debit': float(self.total_debit),
            'total_credit': float(self.total_credit),
            'is_balanced': self.is_balanced,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'posted_at': self.posted_at.isoformat() if self.posted_at else None,
            'entries': [entry.to_dict() for entry in self.entries]
        }

class TransactionEntry(db.Model):
    """نموذج بنود القيد المحاسبي"""
    __tablename__ = 'transaction_entries'
    
    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'), nullable=False)
    debit_amount = db.Column(db.Numeric(15, 2), default=0)
    credit_amount = db.Column(db.Numeric(15, 2), default=0)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<TransactionEntry Account:{self.account_id} Debit:{self.debit_amount} Credit:{self.credit_amount}>'
    
    def to_dict(self):
        """تحويل بند القيد إلى قاموس"""
        return {
            'id': self.id,
            'transaction_id': self.transaction_id,
            'account_id': self.account_id,
            'account_name': self.account.name if self.account else None,
            'account_code': self.account.code if self.account else None,
            'debit_amount': float(self.debit_amount) if self.debit_amount else 0,
            'credit_amount': float(self.credit_amount) if self.credit_amount else 0,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
