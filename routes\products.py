from flask import Blueprint, request, jsonify
from models import db, Product, ProductCategory
from sqlalchemy import or_

products_bp = Blueprint('products', __name__)

@products_bp.route('/', methods=['GET'])
def get_products():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        category_id = request.args.get('category_id', type=int)
        low_stock = request.args.get('low_stock', 'false').lower() == 'true'
        
        query = Product.query
        
        if search:
            query = query.filter(or_(
                Product.name.contains(search),
                Product.code.contains(search),
                Product.barcode.contains(search)
            ))
        
        if category_id:
            query = query.filter(Product.category_id == category_id)
        
        if low_stock:
            query = query.filter(Product.current_stock < Product.min_stock)
        
        query = query.filter(Product.is_active == True).order_by(Product.code)
        products = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'products': [product.to_dict() for product in products.items],
            'total': products.total,
            'pages': products.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@products_bp.route('/', methods=['POST'])
def create_product():
    try:
        data = request.get_json()
        
        if not data.get('code') or not data.get('name'):
            return jsonify({'error': 'الكود والاسم مطلوبان'}), 400
        
        if Product.query.filter_by(code=data['code']).first():
            return jsonify({'error': 'كود المنتج موجود مسبقاً'}), 400
        
        product = Product(**{k: v for k, v in data.items() if hasattr(Product, k)})
        db.session.add(product)
        db.session.commit()
        
        return jsonify(product.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@products_bp.route('/<int:product_id>', methods=['GET'])
def get_product(product_id):
    try:
        product = Product.query.get_or_404(product_id)
        return jsonify(product.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@products_bp.route('/<int:product_id>', methods=['PUT'])
def update_product(product_id):
    try:
        product = Product.query.get_or_404(product_id)
        data = request.get_json()
        
        for field in data:
            if hasattr(product, field):
                setattr(product, field, data[field])
        
        db.session.commit()
        return jsonify(product.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@products_bp.route('/categories', methods=['GET'])
def get_categories():
    try:
        categories = ProductCategory.query.filter_by(is_active=True).order_by(ProductCategory.code).all()
        return jsonify([category.to_dict() for category in categories])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@products_bp.route('/categories', methods=['POST'])
def create_category():
    try:
        data = request.get_json()
        
        if not data.get('code') or not data.get('name'):
            return jsonify({'error': 'الكود والاسم مطلوبان'}), 400
        
        category = ProductCategory(**{k: v for k, v in data.items() if hasattr(ProductCategory, k)})
        db.session.add(category)
        db.session.commit()
        
        return jsonify(category.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
