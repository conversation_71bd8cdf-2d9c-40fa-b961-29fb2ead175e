from models import db, Account, AccountType, Customer, Supplier, Product, ProductCategory, Employee
from datetime import datetime, date
from decimal import Decimal

def init_database():
    """تهيئة قاعدة البيانات بالحسابات الأساسية"""
    
    # إنشاء دليل الحسابات الأساسي
    create_chart_of_accounts()
    
    # حفظ التغييرات
    db.session.commit()
    print("تم إنشاء دليل الحسابات الأساسي")

def create_chart_of_accounts():
    """إنشاء دليل الحسابات الأساسي"""
    
    # الأصول (Assets)
    assets = Account(
        code='1000',
        name='الأصول',
        name_en='Assets',
        account_type=AccountType.ASSET,
        description='مجموعة الأصول الرئيسية'
    )
    db.session.add(assets)
    db.session.flush()  # للحصول على ID
    
    # الأصول المتداولة
    current_assets = Account(
        code='1100',
        name='الأصول المتداولة',
        name_en='Current Assets',
        account_type=AccountType.ASSET,
        parent_id=assets.id
    )
    db.session.add(current_assets)
    db.session.flush()
    
    # النقدية والبنوك
    cash_accounts = [
        Account(code='1101', name='الصندوق', name_en='Cash', account_type=AccountType.ASSET, parent_id=current_assets.id),
        Account(code='1102', name='البنك الأهلي', name_en='National Bank', account_type=AccountType.ASSET, parent_id=current_assets.id),
        Account(code='1103', name='بنك الراجحي', name_en='Al Rajhi Bank', account_type=AccountType.ASSET, parent_id=current_assets.id),
    ]
    
    # العملاء والمدينون
    receivables_accounts = [
        Account(code='1201', name='العملاء', name_en='Accounts Receivable', account_type=AccountType.ASSET, parent_id=current_assets.id),
        Account(code='1202', name='أوراق القبض', name_en='Notes Receivable', account_type=AccountType.ASSET, parent_id=current_assets.id),
        Account(code='1203', name='مدينون متنوعون', name_en='Other Receivables', account_type=AccountType.ASSET, parent_id=current_assets.id),
    ]
    
    # المخزون
    inventory_accounts = [
        Account(code='1301', name='مخزون البضائع', name_en='Merchandise Inventory', account_type=AccountType.ASSET, parent_id=current_assets.id),
        Account(code='1302', name='مخزون المواد الخام', name_en='Raw Materials', account_type=AccountType.ASSET, parent_id=current_assets.id),
    ]
    
    # الخصوم (Liabilities)
    liabilities = Account(
        code='2000',
        name='الخصوم',
        name_en='Liabilities',
        account_type=AccountType.LIABILITY,
        description='مجموعة الخصوم الرئيسية'
    )
    db.session.add(liabilities)
    db.session.flush()
    
    # الخصوم المتداولة
    current_liabilities = Account(
        code='2100',
        name='الخصوم المتداولة',
        name_en='Current Liabilities',
        account_type=AccountType.LIABILITY,
        parent_id=liabilities.id
    )
    db.session.add(current_liabilities)
    db.session.flush()
    
    # الموردون والدائنون
    payables_accounts = [
        Account(code='2101', name='الموردون', name_en='Accounts Payable', account_type=AccountType.LIABILITY, parent_id=current_liabilities.id),
        Account(code='2102', name='أوراق الدفع', name_en='Notes Payable', account_type=AccountType.LIABILITY, parent_id=current_liabilities.id),
        Account(code='2103', name='دائنون متنوعون', name_en='Other Payables', account_type=AccountType.LIABILITY, parent_id=current_liabilities.id),
    ]
    
    # الضرائب والرسوم
    tax_accounts = [
        Account(code='2201', name='ضريبة القيمة المضافة', name_en='VAT Payable', account_type=AccountType.LIABILITY, parent_id=current_liabilities.id),
        Account(code='2202', name='ضريبة الدخل', name_en='Income Tax Payable', account_type=AccountType.LIABILITY, parent_id=current_liabilities.id),
    ]
    
    # حقوق الملكية (Equity)
    equity = Account(
        code='3000',
        name='حقوق الملكية',
        name_en='Equity',
        account_type=AccountType.EQUITY,
        description='مجموعة حقوق الملكية'
    )
    db.session.add(equity)
    db.session.flush()
    
    equity_accounts = [
        Account(code='3101', name='رأس المال', name_en='Capital', account_type=AccountType.EQUITY, parent_id=equity.id),
        Account(code='3201', name='الأرباح المحتجزة', name_en='Retained Earnings', account_type=AccountType.EQUITY, parent_id=equity.id),
    ]
    
    # الإيرادات (Revenue)
    revenue = Account(
        code='4000',
        name='الإيرادات',
        name_en='Revenue',
        account_type=AccountType.REVENUE,
        description='مجموعة الإيرادات'
    )
    db.session.add(revenue)
    db.session.flush()
    
    revenue_accounts = [
        Account(code='4101', name='إيرادات المبيعات', name_en='Sales Revenue', account_type=AccountType.REVENUE, parent_id=revenue.id),
        Account(code='4201', name='إيرادات الخدمات', name_en='Service Revenue', account_type=AccountType.REVENUE, parent_id=revenue.id),
        Account(code='4301', name='إيرادات أخرى', name_en='Other Revenue', account_type=AccountType.REVENUE, parent_id=revenue.id),
    ]
    
    # المصروفات (Expenses)
    expenses = Account(
        code='5000',
        name='المصروفات',
        name_en='Expenses',
        account_type=AccountType.EXPENSE,
        description='مجموعة المصروفات'
    )
    db.session.add(expenses)
    db.session.flush()
    
    expense_accounts = [
        Account(code='5101', name='تكلفة البضاعة المباعة', name_en='Cost of Goods Sold', account_type=AccountType.EXPENSE, parent_id=expenses.id),
        Account(code='5201', name='مصروفات الرواتب', name_en='Salary Expenses', account_type=AccountType.EXPENSE, parent_id=expenses.id),
        Account(code='5301', name='مصروفات الإيجار', name_en='Rent Expenses', account_type=AccountType.EXPENSE, parent_id=expenses.id),
        Account(code='5401', name='مصروفات الكهرباء', name_en='Electricity Expenses', account_type=AccountType.EXPENSE, parent_id=expenses.id),
        Account(code='5501', name='مصروفات إدارية', name_en='Administrative Expenses', account_type=AccountType.EXPENSE, parent_id=expenses.id),
    ]
    
    # إضافة جميع الحسابات
    all_accounts = (cash_accounts + receivables_accounts + inventory_accounts + 
                   payables_accounts + tax_accounts + equity_accounts + 
                   revenue_accounts + expense_accounts)
    
    for account in all_accounts:
        db.session.add(account)

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    # إنشاء فئات المنتجات
    categories = [
        ProductCategory(code='CAT001', name='إلكترونيات', name_en='Electronics'),
        ProductCategory(code='CAT002', name='ملابس', name_en='Clothing'),
        ProductCategory(code='CAT003', name='أثاث', name_en='Furniture'),
    ]
    
    for category in categories:
        db.session.add(category)
    
    db.session.flush()
    
    # إنشاء منتجات تجريبية
    products = [
        Product(code='PROD001', name='لابتوب Dell', category_id=categories[0].id, 
               cost_price=Decimal('2000'), selling_price=Decimal('2500'), current_stock=Decimal('10')),
        Product(code='PROD002', name='قميص قطني', category_id=categories[1].id,
               cost_price=Decimal('50'), selling_price=Decimal('80'), current_stock=Decimal('100')),
        Product(code='PROD003', name='كرسي مكتب', category_id=categories[2].id,
               cost_price=Decimal('300'), selling_price=Decimal('450'), current_stock=Decimal('25')),
    ]
    
    for product in products:
        db.session.add(product)
    
    # إنشاء عملاء تجريبيين
    customers = [
        Customer(code='CUST001', name='شركة الأمل للتجارة', email='<EMAIL>', 
                phone='************', credit_limit=Decimal('50000')),
        Customer(code='CUST002', name='مؤسسة النور', email='<EMAIL>',
                phone='************', credit_limit=Decimal('30000')),
    ]
    
    for customer in customers:
        db.session.add(customer)
    
    # إنشاء موردين تجريبيين
    suppliers = [
        Supplier(code='SUPP001', name='شركة التوريدات المتقدمة', email='<EMAIL>',
                phone='************'),
        Supplier(code='SUPP002', name='مؤسسة الجودة للاستيراد', email='<EMAIL>',
                phone='************'),
    ]
    
    for supplier in suppliers:
        db.session.add(supplier)
    
    # إنشاء موظفين تجريبيين
    employees = [
        Employee(employee_number='EMP001', first_name='أحمد', last_name='محمد',
                email='<EMAIL>', hire_date=date(2023, 1, 15),
                job_title='محاسب', basic_salary=Decimal('8000')),
        Employee(employee_number='EMP002', first_name='فاطمة', last_name='علي',
                email='<EMAIL>', hire_date=date(2023, 3, 1),
                job_title='مدير مبيعات', basic_salary=Decimal('12000')),
    ]
    
    for employee in employees:
        db.session.add(employee)
    
    # حفظ جميع البيانات
    db.session.commit()
    print("تم إنشاء البيانات التجريبية بنجاح")
