from flask import Blueprint, request, jsonify
from models import db, Customer
from sqlalchemy import or_

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/', methods=['GET'])
def get_customers():
    """الحصول على قائمة العملاء"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = Customer.query
        
        if search:
            query = query.filter(or_(
                Customer.name.contains(search),
                Customer.code.contains(search),
                Customer.email.contains(search),
                Customer.phone.contains(search)
            ))
        
        if active_only:
            query = query.filter(Customer.is_active == True)
        
        query = query.order_by(Customer.code)
        customers = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'customers': [customer.to_dict() for customer in customers.items],
            'total': customers.total,
            'pages': customers.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers_bp.route('/<int:customer_id>', methods=['GET'])
def get_customer(customer_id):
    """الحصول على عميل محدد"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        return jsonify(customer.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers_bp.route('/', methods=['POST'])
def create_customer():
    """إنشاء عميل جديد"""
    try:
        data = request.get_json()
        
        required_fields = ['code', 'name']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        if Customer.query.filter_by(code=data['code']).first():
            return jsonify({'error': 'كود العميل موجود مسبقاً'}), 400
        
        if data.get('email') and Customer.query.filter_by(email=data['email']).first():
            return jsonify({'error': 'البريد الإلكتروني موجود مسبقاً'}), 400
        
        customer = Customer(
            code=data['code'],
            name=data['name'],
            name_en=data.get('name_en'),
            email=data.get('email'),
            phone=data.get('phone'),
            mobile=data.get('mobile'),
            address=data.get('address'),
            city=data.get('city'),
            country=data.get('country'),
            postal_code=data.get('postal_code'),
            tax_number=data.get('tax_number'),
            credit_limit=data.get('credit_limit', 0),
            payment_terms=data.get('payment_terms', 30),
            notes=data.get('notes'),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(customer)
        db.session.commit()
        
        return jsonify(customer.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customers_bp.route('/<int:customer_id>', methods=['PUT'])
def update_customer(customer_id):
    """تحديث عميل موجود"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        data = request.get_json()
        
        # التحقق من عدم تكرار الكود
        if 'code' in data and data['code'] != customer.code:
            if Customer.query.filter_by(code=data['code']).first():
                return jsonify({'error': 'كود العميل موجود مسبقاً'}), 400
        
        # التحقق من عدم تكرار البريد الإلكتروني
        if 'email' in data and data['email'] != customer.email:
            if data['email'] and Customer.query.filter_by(email=data['email']).first():
                return jsonify({'error': 'البريد الإلكتروني موجود مسبقاً'}), 400
        
        # تحديث البيانات
        for field in ['code', 'name', 'name_en', 'email', 'phone', 'mobile', 'address', 
                     'city', 'country', 'postal_code', 'tax_number', 'credit_limit', 
                     'payment_terms', 'notes', 'is_active']:
            if field in data:
                setattr(customer, field, data[field])
        
        db.session.commit()
        return jsonify(customer.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customers_bp.route('/<int:customer_id>', methods=['DELETE'])
def delete_customer(customer_id):
    """حذف عميل"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        
        # التحقق من وجود فواتير مرتبطة
        if customer.invoices.count() > 0:
            return jsonify({'error': 'لا يمكن حذف العميل لوجود فواتير مرتبطة'}), 400
        
        db.session.delete(customer)
        db.session.commit()
        
        return jsonify({'message': 'تم حذف العميل بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
