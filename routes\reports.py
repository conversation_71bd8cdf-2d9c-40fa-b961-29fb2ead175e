from flask import Blueprint, request, jsonify
from models import db, Account, AccountType, Transaction, TransactionEntry, Invoice, Customer
from sqlalchemy import func, and_, or_
from datetime import datetime, date

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/trial-balance', methods=['GET'])
def trial_balance():
    """ميزان المراجعة"""
    try:
        date_to = request.args.get('date_to')
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        else:
            date_to = date.today()
        
        # الحصول على جميع الحسابات النشطة
        accounts = Account.query.filter_by(is_active=True).all()
        
        trial_balance_data = []
        total_debit = 0
        total_credit = 0
        
        for account in accounts:
            # حساب الرصيد للحساب حتى التاريخ المحدد
            entries = TransactionEntry.query.join(Transaction).filter(
                TransactionEntry.account_id == account.id,
                Transaction.is_posted == True,
                Transaction.date <= date_to
            ).all()
            
            debit_sum = sum(float(entry.debit_amount) for entry in entries if entry.debit_amount)
            credit_sum = sum(float(entry.credit_amount) for entry in entries if entry.credit_amount)
            
            # تحديد الرصيد حسب نوع الحساب
            if account.account_type in [AccountType.ASSET, AccountType.EXPENSE]:
                balance = debit_sum - credit_sum
                if balance > 0:
                    debit_balance = balance
                    credit_balance = 0
                else:
                    debit_balance = 0
                    credit_balance = abs(balance)
            else:  # LIABILITY, EQUITY, REVENUE
                balance = credit_sum - debit_sum
                if balance > 0:
                    credit_balance = balance
                    debit_balance = 0
                else:
                    credit_balance = 0
                    debit_balance = abs(balance)
            
            if debit_balance != 0 or credit_balance != 0:
                trial_balance_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.value,
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance
                })
                
                total_debit += debit_balance
                total_credit += credit_balance
        
        return jsonify({
            'date_to': date_to.isoformat(),
            'accounts': trial_balance_data,
            'total_debit': total_debit,
            'total_credit': total_credit,
            'is_balanced': abs(total_debit - total_credit) < 0.01
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/balance-sheet', methods=['GET'])
def balance_sheet():
    """الميزانية العمومية"""
    try:
        date_to = request.args.get('date_to')
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        else:
            date_to = date.today()
        
        def get_account_balance(account_type, date_to):
            accounts = Account.query.filter_by(account_type=account_type, is_active=True).all()
            total = 0
            account_details = []
            
            for account in accounts:
                entries = TransactionEntry.query.join(Transaction).filter(
                    TransactionEntry.account_id == account.id,
                    Transaction.is_posted == True,
                    Transaction.date <= date_to
                ).all()
                
                debit_sum = sum(float(entry.debit_amount) for entry in entries if entry.debit_amount)
                credit_sum = sum(float(entry.credit_amount) for entry in entries if entry.credit_amount)
                
                if account_type == AccountType.ASSET:
                    balance = debit_sum - credit_sum
                else:  # LIABILITY, EQUITY
                    balance = credit_sum - debit_sum
                
                if balance != 0:
                    account_details.append({
                        'code': account.code,
                        'name': account.name,
                        'balance': balance
                    })
                    total += balance
            
            return total, account_details
        
        # الأصول
        assets_total, assets_details = get_account_balance(AccountType.ASSET, date_to)
        
        # الخصوم
        liabilities_total, liabilities_details = get_account_balance(AccountType.LIABILITY, date_to)
        
        # حقوق الملكية
        equity_total, equity_details = get_account_balance(AccountType.EQUITY, date_to)
        
        return jsonify({
            'date': date_to.isoformat(),
            'assets': {
                'total': assets_total,
                'accounts': assets_details
            },
            'liabilities': {
                'total': liabilities_total,
                'accounts': liabilities_details
            },
            'equity': {
                'total': equity_total,
                'accounts': equity_details
            },
            'total_liabilities_equity': liabilities_total + equity_total,
            'is_balanced': abs(assets_total - (liabilities_total + equity_total)) < 0.01
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/income-statement', methods=['GET'])
def income_statement():
    """قائمة الدخل"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not date_from or not date_to:
            return jsonify({'error': 'تاريخ البداية والنهاية مطلوبان'}), 400
        
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        
        def get_account_balance(account_type, date_from, date_to):
            accounts = Account.query.filter_by(account_type=account_type, is_active=True).all()
            total = 0
            account_details = []
            
            for account in accounts:
                entries = TransactionEntry.query.join(Transaction).filter(
                    TransactionEntry.account_id == account.id,
                    Transaction.is_posted == True,
                    Transaction.date >= date_from,
                    Transaction.date <= date_to
                ).all()
                
                debit_sum = sum(float(entry.debit_amount) for entry in entries if entry.debit_amount)
                credit_sum = sum(float(entry.credit_amount) for entry in entries if entry.credit_amount)
                
                if account_type == AccountType.REVENUE:
                    balance = credit_sum - debit_sum
                else:  # EXPENSE
                    balance = debit_sum - credit_sum
                
                if balance != 0:
                    account_details.append({
                        'code': account.code,
                        'name': account.name,
                        'balance': balance
                    })
                    total += balance
            
            return total, account_details
        
        # الإيرادات
        revenue_total, revenue_details = get_account_balance(AccountType.REVENUE, date_from, date_to)
        
        # المصروفات
        expense_total, expense_details = get_account_balance(AccountType.EXPENSE, date_from, date_to)
        
        # صافي الربح/الخسارة
        net_income = revenue_total - expense_total
        
        return jsonify({
            'date_from': date_from.isoformat(),
            'date_to': date_to.isoformat(),
            'revenue': {
                'total': revenue_total,
                'accounts': revenue_details
            },
            'expenses': {
                'total': expense_total,
                'accounts': expense_details
            },
            'net_income': net_income
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/customer-aging', methods=['GET'])
def customer_aging():
    """تقرير أعمار الديون للعملاء"""
    try:
        as_of_date = request.args.get('as_of_date')
        if as_of_date:
            as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
        else:
            as_of_date = date.today()
        
        customers = Customer.query.filter_by(is_active=True).all()
        aging_data = []
        
        for customer in customers:
            invoices = Invoice.query.filter(
                Invoice.customer_id == customer.id,
                Invoice.is_posted == True,
                Invoice.is_paid == False
            ).all()
            
            if not invoices:
                continue
            
            current = 0  # غير مستحق
            days_30 = 0  # 1-30 يوم
            days_60 = 0  # 31-60 يوم
            days_90 = 0  # 61-90 يوم
            over_90 = 0  # أكثر من 90 يوم
            
            for invoice in invoices:
                remaining = float(invoice.remaining_amount)
                days_overdue = (as_of_date - invoice.due_date).days
                
                if days_overdue <= 0:
                    current += remaining
                elif days_overdue <= 30:
                    days_30 += remaining
                elif days_overdue <= 60:
                    days_60 += remaining
                elif days_overdue <= 90:
                    days_90 += remaining
                else:
                    over_90 += remaining
            
            total_balance = current + days_30 + days_60 + days_90 + over_90
            
            if total_balance > 0:
                aging_data.append({
                    'customer_code': customer.code,
                    'customer_name': customer.name,
                    'current': current,
                    'days_1_30': days_30,
                    'days_31_60': days_60,
                    'days_61_90': days_90,
                    'over_90_days': over_90,
                    'total_balance': total_balance
                })
        
        return jsonify({
            'as_of_date': as_of_date.isoformat(),
            'customers': aging_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
