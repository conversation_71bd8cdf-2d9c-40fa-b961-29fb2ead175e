from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_migrate import Migrate
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from models import db
from utils.database_init import init_database, create_sample_data

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    app = Flask(__name__)
    
    # تحديد بيئة التشغيل
    config_name = config_name or os.environ.get('FLASK_ENV', 'development')
    app.config.from_object(config[config_name])
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # تسجيل المسارات
    register_blueprints(app)
    
    # إنشاء الجداول
    with app.app_context():
        db.create_all()
    
    return app

def register_blueprints(app):
    """تسجيل مسارات التطبيق"""
    from routes.main import main_bp
    from routes.accounts import accounts_bp
    from routes.transactions import transactions_bp
    from routes.customers import customers_bp
    from routes.suppliers import suppliers_bp
    from routes.products import products_bp
    from routes.employees import employees_bp
    from routes.invoices import invoices_bp
    from routes.reports import reports_bp
    
    app.register_blueprint(main_bp)
    app.register_blueprint(accounts_bp, url_prefix='/api/accounts')
    app.register_blueprint(transactions_bp, url_prefix='/api/transactions')
    app.register_blueprint(customers_bp, url_prefix='/api/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/api/suppliers')
    app.register_blueprint(products_bp, url_prefix='/api/products')
    app.register_blueprint(employees_bp, url_prefix='/api/employees')
    app.register_blueprint(invoices_bp, url_prefix='/api/invoices')
    app.register_blueprint(reports_bp, url_prefix='/api/reports')

# إنشاء التطبيق
app = create_app()

@app.cli.command()
def init_db():
    """تهيئة قاعدة البيانات"""
    print("تهيئة قاعدة البيانات...")
    with app.app_context():
        db.create_all()
        init_database()
        print("تم إنشاء قاعدة البيانات بنجاح!")

@app.cli.command()
def create_sample():
    """إنشاء بيانات تجريبية"""
    print("إنشاء بيانات تجريبية...")
    with app.app_context():
        create_sample_data()
        print("تم إنشاء البيانات التجريبية بنجاح!")

@app.errorhandler(404)
def not_found(error):
    """معالج خطأ 404"""
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    """معالج خطأ 500"""
    db.session.rollback()
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

if __name__ == '__main__':
    # تشغيل التطبيق
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"تشغيل نظام المحاسبة على المنفذ {port}")
    print(f"افتح المتصفح وانتقل إلى: http://localhost:{port}")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
