from flask import Blueprint, request, jsonify
from models import db, Supplier
from sqlalchemy import or_

suppliers_bp = Blueprint('suppliers', __name__)

@suppliers_bp.route('/', methods=['GET'])
def get_suppliers():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = Supplier.query
        
        if search:
            query = query.filter(or_(
                Supplier.name.contains(search),
                Supplier.code.contains(search),
                Supplier.email.contains(search)
            ))
        
        if active_only:
            query = query.filter(Supplier.is_active == True)
        
        query = query.order_by(Supplier.code)
        suppliers = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'suppliers': [supplier.to_dict() for supplier in suppliers.items],
            'total': suppliers.total,
            'pages': suppliers.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/', methods=['POST'])
def create_supplier():
    try:
        data = request.get_json()
        
        if not data.get('code') or not data.get('name'):
            return jsonify({'error': 'الكود والاسم مطلوبان'}), 400
        
        if Supplier.query.filter_by(code=data['code']).first():
            return jsonify({'error': 'كود المورد موجود مسبقاً'}), 400
        
        supplier = Supplier(**{k: v for k, v in data.items() if hasattr(Supplier, k)})
        db.session.add(supplier)
        db.session.commit()
        
        return jsonify(supplier.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/<int:supplier_id>', methods=['GET'])
def get_supplier(supplier_id):
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        return jsonify(supplier.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/<int:supplier_id>', methods=['PUT'])
def update_supplier(supplier_id):
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        data = request.get_json()
        
        for field in data:
            if hasattr(supplier, field):
                setattr(supplier, field, data[field])
        
        db.session.commit()
        return jsonify(supplier.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/<int:supplier_id>', methods=['DELETE'])
def delete_supplier(supplier_id):
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        db.session.delete(supplier)
        db.session.commit()
        return jsonify({'message': 'تم حذف المورد بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
