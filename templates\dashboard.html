{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-speedometer2"></i> لوحة التحكم</h2>
    <button class="btn btn-primary" onclick="refreshDashboard()">
        <i class="bi bi-arrow-clockwise"></i> تحديث
    </button>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">الحسابات</h6>
                        <h3 id="accountsCount">-</h3>
                    </div>
                    <i class="bi bi-journal-text display-6"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">العملاء</h6>
                        <h3 id="customersCount">-</h3>
                    </div>
                    <i class="bi bi-people display-6"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المنتجات</h6>
                        <h3 id="productsCount">-</h3>
                    </div>
                    <i class="bi bi-box display-6"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">الفواتير</h6>
                        <h3 id="invoicesCount">-</h3>
                    </div>
                    <i class="bi bi-receipt display-6"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات مالية -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-currency-dollar"></i> الإحصائيات المالية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6>إجمالي المبيعات</h6>
                        <h4 class="text-success" id="totalSales">0 ر.س</h4>
                    </div>
                    <div class="col-6">
                        <h6>المبالغ المستحقة</h6>
                        <h4 class="text-warning" id="totalReceivables">0 ر.س</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-exclamation-triangle"></i> تنبيهات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6>فواتير غير مدفوعة</h6>
                        <h4 class="text-danger" id="unpaidInvoices">0</h4>
                    </div>
                    <div class="col-6">
                        <h6>منتجات منخفضة المخزون</h6>
                        <h4 class="text-warning" id="lowStockProducts">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أحدث الفواتير -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-receipt"></i> أحدث الفواتير</h5>
                <a href="{{ url_for('main.invoices_page') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody id="recentInvoicesTable">
                            <tr>
                                <td colspan="4" class="text-center">جاري التحميل...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- المنتجات منخفضة المخزون -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-exclamation-triangle text-warning"></i> منتجات منخفضة المخزون</h5>
                <a href="{{ url_for('main.products_page') }}" class="btn btn-sm btn-outline-warning">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>كود المنتج</th>
                                <th>اسم المنتج</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                            </tr>
                        </thead>
                        <tbody id="lowStockTable">
                            <tr>
                                <td colspan="4" class="text-center">جاري التحميل...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.transactions_page') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-plus-circle"></i> قيد محاسبي جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.invoices_page') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-plus-circle"></i> فاتورة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.customers_page') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-plus-circle"></i> عميل جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.products_page') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-plus-circle"></i> منتج جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل بيانات لوحة التحكم
function loadDashboardData() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            document.getElementById('accountsCount').textContent = data.accounts_count || 0;
            document.getElementById('customersCount').textContent = data.customers_count || 0;
            document.getElementById('productsCount').textContent = data.products_count || 0;
            document.getElementById('invoicesCount').textContent = data.invoices_count || 0;
            
            // تحديث الإحصائيات المالية
            document.getElementById('totalSales').textContent = formatCurrency(data.total_sales || 0);
            document.getElementById('totalReceivables').textContent = formatCurrency(data.total_receivables || 0);
            document.getElementById('unpaidInvoices').textContent = data.unpaid_invoices_count || 0;
            document.getElementById('lowStockProducts').textContent = data.low_stock_products?.length || 0;
            
            // تحديث جدول أحدث الفواتير
            updateRecentInvoicesTable(data.recent_invoices || []);
            
            // تحديث جدول المنتجات منخفضة المخزون
            updateLowStockTable(data.low_stock_products || []);
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        });
}

function updateRecentInvoicesTable(invoices) {
    const tbody = document.getElementById('recentInvoicesTable');
    if (invoices.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد فواتير</td></tr>';
        return;
    }
    
    tbody.innerHTML = invoices.map(invoice => `
        <tr>
            <td>${invoice.invoice_number}</td>
            <td>${invoice.customer_name || '-'}</td>
            <td>${formatCurrency(invoice.total_amount)}</td>
            <td>${formatDate(invoice.invoice_date)}</td>
        </tr>
    `).join('');
}

function updateLowStockTable(products) {
    const tbody = document.getElementById('lowStockTable');
    if (products.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد منتجات منخفضة المخزون</td></tr>';
        return;
    }
    
    tbody.innerHTML = products.map(product => `
        <tr>
            <td>${product.code}</td>
            <td>${product.name}</td>
            <td class="text-warning">${product.current_stock}</td>
            <td>${product.min_stock}</td>
        </tr>
    `).join('');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function refreshDashboard() {
    loadDashboardData();
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadDashboardData);
</script>
{% endblock %}
