from flask import Blueprint, render_template, jsonify
from models import db, Account, Customer, Supplier, Product, Invoice, Employee
from sqlalchemy import func

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@main_bp.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    return render_template('dashboard.html')

@main_bp.route('/api/dashboard/stats')
def dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        # إحصائيات أساسية
        stats = {
            'accounts_count': Account.query.filter_by(is_active=True).count(),
            'customers_count': Customer.query.filter_by(is_active=True).count(),
            'suppliers_count': Supplier.query.filter_by(is_active=True).count(),
            'products_count': Product.query.filter_by(is_active=True).count(),
            'employees_count': Employee.query.filter_by(is_active=True).count(),
            'invoices_count': Invoice.query.count(),
            'posted_invoices_count': Invoice.query.filter_by(is_posted=True).count(),
            'unpaid_invoices_count': Invoice.query.filter_by(is_paid=False).count(),
        }
        
        # إحصائيات مالية
        total_receivables = db.session.query(func.sum(Invoice.total_amount - Invoice.paid_amount)).filter(
            Invoice.is_posted == True,
            Invoice.is_paid == False
        ).scalar() or 0
        
        total_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
            Invoice.is_posted == True
        ).scalar() or 0
        
        stats.update({
            'total_receivables': float(total_receivables),
            'total_sales': float(total_sales),
        })
        
        # أحدث الفواتير
        recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
        stats['recent_invoices'] = [invoice.to_dict() for invoice in recent_invoices]
        
        # المنتجات منخفضة المخزون
        low_stock_products = Product.query.filter(
            Product.current_stock < Product.min_stock,
            Product.min_stock > 0,
            Product.is_active == True
        ).limit(5).all()
        stats['low_stock_products'] = [product.to_dict() for product in low_stock_products]
        
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@main_bp.route('/accounts')
def accounts_page():
    """صفحة الحسابات"""
    return render_template('accounts.html')

@main_bp.route('/transactions')
def transactions_page():
    """صفحة المعاملات"""
    return render_template('transactions.html')

@main_bp.route('/customers')
def customers_page():
    """صفحة العملاء"""
    return render_template('customers.html')

@main_bp.route('/suppliers')
def suppliers_page():
    """صفحة الموردين"""
    return render_template('suppliers.html')

@main_bp.route('/products')
def products_page():
    """صفحة المنتجات"""
    return render_template('products.html')

@main_bp.route('/employees')
def employees_page():
    """صفحة الموظفين"""
    return render_template('employees.html')

@main_bp.route('/invoices')
def invoices_page():
    """صفحة الفواتير"""
    return render_template('invoices.html')

@main_bp.route('/reports')
def reports_page():
    """صفحة التقارير"""
    return render_template('reports.html')
