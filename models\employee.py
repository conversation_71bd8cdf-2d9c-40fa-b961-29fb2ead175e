from . import db
from datetime import datetime

class Employee(db.Model):
    """نموذج الموظفين"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_number = db.Column(db.String(20), unique=True, nullable=False, index=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    national_id = db.Column(db.String(20), unique=True)  # رقم الهوية
    passport_number = db.Column(db.String(20))  # رقم الجواز
    birth_date = db.Column(db.Date)
    hire_date = db.Column(db.Date, nullable=False)
    termination_date = db.Column(db.Date)
    job_title = db.Column(db.String(100))
    department = db.Column(db.String(100))
    basic_salary = db.Column(db.Numeric(15, 2), default=0)
    allowances = db.Column(db.Numeric(15, 2), default=0)  # البدلات
    is_active = db.Column(db.Boolean, default=True)
    bank_name = db.Column(db.String(100))
    bank_account = db.Column(db.String(50))
    emergency_contact_name = db.Column(db.String(100))
    emergency_contact_phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Employee {self.employee_number}: {self.full_name}>'
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        """العمر"""
        if self.birth_date:
            today = datetime.utcnow().date()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None
    
    @property
    def years_of_service(self):
        """سنوات الخدمة"""
        if self.hire_date:
            end_date = self.termination_date or datetime.utcnow().date()
            return end_date.year - self.hire_date.year
        return 0
    
    @property
    def total_salary(self):
        """إجمالي الراتب"""
        return float(self.basic_salary + self.allowances) if self.basic_salary and self.allowances else float(self.basic_salary or 0)
    
    def to_dict(self):
        """تحويل الموظف إلى قاموس"""
        return {
            'id': self.id,
            'employee_number': self.employee_number,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'address': self.address,
            'city': self.city,
            'national_id': self.national_id,
            'passport_number': self.passport_number,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'termination_date': self.termination_date.isoformat() if self.termination_date else None,
            'job_title': self.job_title,
            'department': self.department,
            'basic_salary': float(self.basic_salary) if self.basic_salary else 0,
            'allowances': float(self.allowances) if self.allowances else 0,
            'total_salary': self.total_salary,
            'is_active': self.is_active,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'emergency_contact_name': self.emergency_contact_name,
            'emergency_contact_phone': self.emergency_contact_phone,
            'notes': self.notes,
            'age': self.age,
            'years_of_service': self.years_of_service,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
