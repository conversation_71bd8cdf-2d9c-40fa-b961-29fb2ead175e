import unittest
import sys
import os

# إضافة المجلد الجذر إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, Account, AccountType, Customer, Product
from config import TestingConfig

class BasicTestCase(unittest.TestCase):
    """اختبارات أساسية للنظام"""
    
    def setUp(self):
        """إعداد البيئة للاختبار"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        # إنشاء الجداول
        db.create_all()
    
    def tearDown(self):
        """تنظيف البيئة بعد الاختبار"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_app_creation(self):
        """اختبار إنشاء التطبيق"""
        self.assertIsNotNone(self.app)
        self.assertTrue(self.app.config['TESTING'])
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # إنشاء حساب تجريبي
        account = Account(
            code='1001',
            name='حساب تجريبي',
            account_type=AccountType.ASSET
        )
        db.session.add(account)
        db.session.commit()
        
        # التحقق من الحفظ
        saved_account = Account.query.filter_by(code='1001').first()
        self.assertIsNotNone(saved_account)
        self.assertEqual(saved_account.name, 'حساب تجريبي')
    
    def test_account_model(self):
        """اختبار نموذج الحسابات"""
        # إنشاء حساب أب
        parent_account = Account(
            code='1000',
            name='الأصول',
            account_type=AccountType.ASSET
        )
        db.session.add(parent_account)
        db.session.flush()
        
        # إنشاء حساب فرعي
        child_account = Account(
            code='1100',
            name='الأصول المتداولة',
            account_type=AccountType.ASSET,
            parent_id=parent_account.id
        )
        db.session.add(child_account)
        db.session.commit()
        
        # اختبار العلاقات
        self.assertEqual(child_account.parent, parent_account)
        self.assertIn(child_account, parent_account.children)
        
        # اختبار الخصائص
        self.assertEqual(child_account.level, 1)
        self.assertEqual(parent_account.level, 0)
    
    def test_customer_model(self):
        """اختبار نموذج العملاء"""
        customer = Customer(
            code='CUST001',
            name='عميل تجريبي',
            email='<EMAIL>',
            credit_limit=10000
        )
        db.session.add(customer)
        db.session.commit()
        
        # التحقق من الحفظ
        saved_customer = Customer.query.filter_by(code='CUST001').first()
        self.assertIsNotNone(saved_customer)
        self.assertEqual(saved_customer.name, 'عميل تجريبي')
        self.assertEqual(float(saved_customer.credit_limit), 10000.0)
    
    def test_api_endpoints(self):
        """اختبار نقاط النهاية الأساسية"""
        # اختبار الصفحة الرئيسية
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        
        # اختبار لوحة التحكم
        response = self.client.get('/dashboard')
        self.assertEqual(response.status_code, 200)
        
        # اختبار API الحسابات
        response = self.client.get('/api/accounts/')
        self.assertEqual(response.status_code, 200)
        
        data = response.get_json()
        self.assertIn('accounts', data)
        self.assertIn('total', data)
    
    def test_account_creation_api(self):
        """اختبار إنشاء حساب عبر API"""
        account_data = {
            'code': '1001',
            'name': 'حساب تجريبي',
            'account_type': 'ASSET',
            'description': 'حساب للاختبار'
        }
        
        response = self.client.post('/api/accounts/', 
                                  json=account_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        
        data = response.get_json()
        self.assertEqual(data['code'], '1001')
        self.assertEqual(data['name'], 'حساب تجريبي')
        
        # التحقق من الحفظ في قاعدة البيانات
        account = Account.query.filter_by(code='1001').first()
        self.assertIsNotNone(account)
    
    def test_customer_creation_api(self):
        """اختبار إنشاء عميل عبر API"""
        customer_data = {
            'code': 'CUST001',
            'name': 'عميل تجريبي',
            'email': '<EMAIL>',
            'phone': '**********',
            'credit_limit': 15000
        }
        
        response = self.client.post('/api/customers/', 
                                  json=customer_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        
        data = response.get_json()
        self.assertEqual(data['code'], 'CUST001')
        self.assertEqual(data['name'], 'عميل تجريبي')
        
        # التحقق من الحفظ في قاعدة البيانات
        customer = Customer.query.filter_by(code='CUST001').first()
        self.assertIsNotNone(customer)
    
    def test_dashboard_stats(self):
        """اختبار إحصائيات لوحة التحكم"""
        # إنشاء بيانات تجريبية
        account = Account(code='1001', name='حساب تجريبي', account_type=AccountType.ASSET)
        customer = Customer(code='CUST001', name='عميل تجريبي')
        
        db.session.add_all([account, customer])
        db.session.commit()
        
        # اختبار API الإحصائيات
        response = self.client.get('/api/dashboard/stats')
        self.assertEqual(response.status_code, 200)
        
        data = response.get_json()
        self.assertGreaterEqual(data['accounts_count'], 1)
        self.assertGreaterEqual(data['customers_count'], 1)

class AccountBalanceTestCase(unittest.TestCase):
    """اختبارات حساب الأرصدة"""
    
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
    
    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_asset_account_balance(self):
        """اختبار حساب رصيد حساب الأصول"""
        from models import Transaction, TransactionEntry
        
        # إنشاء حساب أصول
        asset_account = Account(
            code='1001',
            name='الصندوق',
            account_type=AccountType.ASSET
        )
        db.session.add(asset_account)
        db.session.flush()
        
        # إنشاء معاملة
        transaction = Transaction(
            transaction_number='JE001',
            description='قيد افتتاحي',
            total_amount=1000
        )
        db.session.add(transaction)
        db.session.flush()
        
        # إنشاء بند القيد
        entry = TransactionEntry(
            transaction_id=transaction.id,
            account_id=asset_account.id,
            debit_amount=1000,
            credit_amount=0
        )
        db.session.add(entry)
        db.session.commit()
        
        # اختبار الرصيد
        self.assertEqual(float(asset_account.balance), 1000.0)

if __name__ == '__main__':
    unittest.main()
