from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

# استيراد جميع النماذج
from .account import Account, AccountType
from .transaction import Transaction, TransactionEntry
from .customer import Customer
from .supplier import Supplier
from .product import Product, ProductCategory
from .employee import Employee
from .invoice import Invoice, InvoiceItem

__all__ = [
    'db',
    'Account',
    'AccountType', 
    'Transaction',
    'TransactionEntry',
    'Customer',
    'Supplier',
    'Product',
    'ProductCategory',
    'Employee',
    'Invoice',
    'InvoiceItem'
]
