/* تنسيقات مخصصة لنظام المحاسبة */

/* إعدادات عامة للغة العربية */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين شكل البطاقات */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* تنسيق الجداول */
.table {
    font-size: 0.9rem;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.8rem;
}

/* تنسيق النماذج */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تنسيق التنبيهات */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* تنسيق شريط التنقل */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* تنسيق القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* تنسيق الصفحة الرئيسية */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 1rem;
}

/* تنسيق بطاقات الإحصائيات */
.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-info {
    border-radius: 1rem;
}

.card.bg-primary .card-body, 
.card.bg-success .card-body, 
.card.bg-warning .card-body, 
.card.bg-info .card-body {
    padding: 1.5rem;
}

/* تنسيق الأيقونات */
.display-1 {
    font-size: 4rem;
    opacity: 0.8;
}

.display-6 {
    font-size: 2rem;
    opacity: 0.8;
}

/* تنسيق الجداول المتجاوبة */
.table-responsive {
    border-radius: 0.5rem;
}

/* تنسيق المودال */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 1rem 1rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 1rem 1rem;
}

/* تنسيق التحميل */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تنسيق الحالات */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* تنسيق الأرقام */
.text-currency {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* تنسيق الفواصل */
hr {
    margin: 2rem 0;
    opacity: 0.1;
}

/* تنسيق التذييل */
footer {
    margin-top: auto;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .display-1 {
        font-size: 2.5rem;
    }
    
    .display-6 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
}

/* تنسيق حالات الفواتير */
.invoice-status-paid {
    color: #198754;
    font-weight: 600;
}

.invoice-status-unpaid {
    color: #dc3545;
    font-weight: 600;
}

.invoice-status-overdue {
    color: #fd7e14;
    font-weight: 600;
}

/* تنسيق حالات المخزون */
.stock-low {
    color: #dc3545;
    font-weight: 600;
}

.stock-normal {
    color: #198754;
    font-weight: 600;
}

/* تنسيق الأزرار العائمة */
.btn-floating {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: 1000;
    border-radius: 50%;
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* تحسين شكل النماذج */
.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control,
.form-floating > .form-select {
    padding: 1rem 0.75rem;
}

/* تنسيق شجرة الحسابات */
.account-tree {
    list-style: none;
    padding-right: 0;
}

.account-tree li {
    margin: 0.25rem 0;
    padding-right: 1rem;
    position: relative;
}

.account-tree li::before {
    content: '';
    position: absolute;
    right: -0.5rem;
    top: 0.75rem;
    width: 0.5rem;
    height: 1px;
    background-color: #dee2e6;
}

.account-tree li:last-child::after {
    content: '';
    position: absolute;
    right: -0.5rem;
    top: 0.75rem;
    bottom: 0;
    width: 1px;
    background-color: #dee2e6;
}

/* تنسيق البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}
