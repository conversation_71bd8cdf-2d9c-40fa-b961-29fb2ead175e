# نظام المحاسبة المتكامل للشركات

نظام محاسبة شامل مطور بـ Python و Flask لإدارة العمليات المحاسبية للشركات.

## المميزات

- 📊 **إدارة الحسابات**: دليل حسابات محاسبي شامل
- 💰 **إدارة المعاملات**: قيود محاسبية وفواتير
- 👥 **إدارة العملاء والموردين**: قاعدة بيانات الأطراف الخارجية
- 📈 **التقارير المالية**: ميزانية عمومية وقائمة دخل
- 📦 **إدارة المخزون**: تتبع البضائع والمواد
- 💼 **إدارة الرواتب**: حساب وإدارة رواتب الموظفين

## التثبيت

1. إنشاء بيئة افتراضية:
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
venv\Scripts\activate     # على Windows
```

2. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

3. إعداد قاعدة البيانات:
```bash
python app.py init-db
```

4. تشغيل التطبيق:
```bash
python app.py
```

## الاستخدام

افتح المتصفح وانتقل إلى `http://localhost:5000` لبدء استخدام النظام.

## الهيكل

```
ERP/
├── app.py                 # التطبيق الرئيسي
├── models/               # نماذج قاعدة البيانات
├── routes/               # مسارات API
├── templates/            # قوالب HTML
├── static/               # ملفات CSS/JS
├── utils/                # أدوات مساعدة
└── tests/                # اختبارات
```

## الترخيص

MIT License
