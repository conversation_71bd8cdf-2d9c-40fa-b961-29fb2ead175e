from . import db
from datetime import datetime

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    country = db.Column(db.String(50))
    postal_code = db.Column(db.String(10))
    tax_number = db.Column(db.String(50))  # الرقم الضريبي
    credit_limit = db.Column(db.Numeric(15, 2), default=0)  # حد الائتمان
    payment_terms = db.Column(db.Integer, default=30)  # شروط الدفع بالأيام
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic')
    
    def __repr__(self):
        return f'<Customer {self.code}: {self.name}>'
    
    @property
    def balance(self):
        """حساب رصيد العميل"""
        # حساب إجمالي الفواتير
        total_invoices = sum(invoice.total_amount for invoice in self.invoices if invoice.is_posted)
        # حساب إجمالي المدفوعات (يجب إضافة نموذج المدفوعات لاحقاً)
        total_payments = 0  # TODO: حساب المدفوعات الفعلية
        return total_invoices - total_payments
    
    @property
    def overdue_amount(self):
        """المبلغ المستحق"""
        overdue = 0
        for invoice in self.invoices:
            if invoice.is_overdue:
                overdue += invoice.remaining_amount
        return overdue
    
    def to_dict(self):
        """تحويل العميل إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'address': self.address,
            'city': self.city,
            'country': self.country,
            'postal_code': self.postal_code,
            'tax_number': self.tax_number,
            'credit_limit': float(self.credit_limit) if self.credit_limit else 0,
            'payment_terms': self.payment_terms,
            'is_active': self.is_active,
            'notes': self.notes,
            'balance': float(self.balance),
            'overdue_amount': float(self.overdue_amount),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
