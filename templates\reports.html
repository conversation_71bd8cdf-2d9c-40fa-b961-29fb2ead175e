{% extends "base.html" %}

{% block title %}التقارير - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-graph-up"></i> التقارير المالية</h2>
</div>

<!-- بطاقات التقارير -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-balance-scale display-1 text-primary"></i>
                <h5 class="card-title">ميزان المراجعة</h5>
                <p class="card-text">عرض أرصدة جميع الحسابات</p>
                <button class="btn btn-primary" onclick="showTrialBalance()">عرض التقرير</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-clipboard-data display-1 text-success"></i>
                <h5 class="card-title">الميزانية العمومية</h5>
                <p class="card-text">الأصول والخصوم وحقوق الملكية</p>
                <button class="btn btn-success" onclick="showBalanceSheet()">عرض التقرير</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-graph-up-arrow display-1 text-info"></i>
                <h5 class="card-title">قائمة الدخل</h5>
                <p class="card-text">الإيرادات والمصروفات وصافي الربح</p>
                <button class="btn btn-info" onclick="showIncomeStatement()">عرض التقرير</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-people display-1 text-warning"></i>
                <h5 class="card-title">أعمار ديون العملاء</h5>
                <p class="card-text">تحليل المستحقات حسب فترات الاستحقاق</p>
                <button class="btn btn-warning" onclick="showCustomerAging()">عرض التقرير</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-receipt display-1 text-danger"></i>
                <h5 class="card-title">تقرير المبيعات</h5>
                <p class="card-text">تحليل المبيعات والفواتير</p>
                <button class="btn btn-danger" onclick="showSalesReport()">عرض التقرير</button>
            </div>
        </div>
    </div>
</div>

<!-- منطقة عرض التقارير -->
<div id="reportContainer" class="mt-4" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 id="reportTitle">التقرير</h5>
            <div>
                <button class="btn btn-sm btn-outline-primary" onclick="printReport()">
                    <i class="bi bi-printer"></i> طباعة
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="exportReport()">
                    <i class="bi bi-download"></i> تصدير
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="hideReport()">
                    <i class="bi bi-x"></i> إغلاق
                </button>
            </div>
        </div>
        <div class="card-body" id="reportContent">
            <!-- محتوى التقرير سيتم تحميله هنا -->
        </div>
    </div>
</div>

<!-- مودال إعدادات التقرير -->
<div class="modal fade" id="reportSettingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعدادات التقرير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="reportDateFrom" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="reportDateFrom">
                </div>
                <div class="mb-3">
                    <label for="reportDateTo" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="reportDateTo">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="generateReport()">إنشاء التقرير</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentReportType = '';

// عرض ميزان المراجعة
async function showTrialBalance() {
    currentReportType = 'trial-balance';
    
    // تعيين التاريخ الافتراضي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reportDateTo').value = today;
    document.getElementById('reportDateFrom').style.display = 'none';
    document.querySelector('label[for="reportDateFrom"]').style.display = 'none';
    
    showModal('#reportSettingsModal');
}

// عرض الميزانية العمومية
async function showBalanceSheet() {
    currentReportType = 'balance-sheet';
    
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reportDateTo').value = today;
    document.getElementById('reportDateFrom').style.display = 'none';
    document.querySelector('label[for="reportDateFrom"]').style.display = 'none';
    
    showModal('#reportSettingsModal');
}

// عرض قائمة الدخل
async function showIncomeStatement() {
    currentReportType = 'income-statement';
    
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('reportDateFrom').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('reportDateTo').value = today.toISOString().split('T')[0];
    document.getElementById('reportDateFrom').style.display = 'block';
    document.querySelector('label[for="reportDateFrom"]').style.display = 'block';
    
    showModal('#reportSettingsModal');
}

// عرض أعمار ديون العملاء
async function showCustomerAging() {
    currentReportType = 'customer-aging';
    
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reportDateTo').value = today;
    document.getElementById('reportDateFrom').style.display = 'none';
    document.querySelector('label[for="reportDateFrom"]').style.display = 'none';
    
    showModal('#reportSettingsModal');
}

// إنشاء التقرير
async function generateReport() {
    try {
        hideModal('#reportSettingsModal');
        showReport();
        showLoading('#reportContent');
        
        let url = `/reports/${currentReportType}`;
        const params = new URLSearchParams();
        
        if (document.getElementById('reportDateFrom').style.display !== 'none') {
            params.append('date_from', document.getElementById('reportDateFrom').value);
        }
        params.append('date_to', document.getElementById('reportDateTo').value);
        
        if (params.toString()) {
            url += `?${params}`;
        }
        
        const data = await apiRequest(url);
        displayReport(data);
        
    } catch (error) {
        showError('#reportContent', error.message);
    }
}

// عرض التقرير
function showReport() {
    document.getElementById('reportContainer').style.display = 'block';
    document.getElementById('reportContainer').scrollIntoView({ behavior: 'smooth' });
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContainer').style.display = 'none';
}

// عرض محتوى التقرير
function displayReport(data) {
    const reportContent = document.getElementById('reportContent');
    let html = '';
    
    switch (currentReportType) {
        case 'trial-balance':
            document.getElementById('reportTitle').textContent = 'ميزان المراجعة';
            html = generateTrialBalanceHTML(data);
            break;
        case 'balance-sheet':
            document.getElementById('reportTitle').textContent = 'الميزانية العمومية';
            html = generateBalanceSheetHTML(data);
            break;
        case 'income-statement':
            document.getElementById('reportTitle').textContent = 'قائمة الدخل';
            html = generateIncomeStatementHTML(data);
            break;
        case 'customer-aging':
            document.getElementById('reportTitle').textContent = 'أعمار ديون العملاء';
            html = generateCustomerAgingHTML(data);
            break;
    }
    
    reportContent.innerHTML = html;
}

// إنشاء HTML لميزان المراجعة
function generateTrialBalanceHTML(data) {
    let html = `
        <div class="text-center mb-3">
            <h4>ميزان المراجعة</h4>
            <p>كما في ${formatDate(data.date_to)}</p>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>كود الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.accounts.forEach(account => {
        html += `
            <tr>
                <td>${account.account_code}</td>
                <td>${account.account_name}</td>
                <td class="text-end">${account.debit_balance > 0 ? formatCurrency(account.debit_balance) : '-'}</td>
                <td class="text-end">${account.credit_balance > 0 ? formatCurrency(account.credit_balance) : '-'}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th colspan="2">الإجمالي</th>
                        <th class="text-end">${formatCurrency(data.total_debit)}</th>
                        <th class="text-end">${formatCurrency(data.total_credit)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;
    
    if (!data.is_balanced) {
        html += '<div class="alert alert-warning">تحذير: الميزان غير متوازن!</div>';
    }
    
    return html;
}

// إنشاء HTML للميزانية العمومية
function generateBalanceSheetHTML(data) {
    let html = `
        <div class="text-center mb-3">
            <h4>الميزانية العمومية</h4>
            <p>كما في ${formatDate(data.date)}</p>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h5>الأصول</h5>
                <table class="table table-sm">
    `;
    
    data.assets.accounts.forEach(account => {
        html += `
            <tr>
                <td>${account.name}</td>
                <td class="text-end">${formatCurrency(account.balance)}</td>
            </tr>
        `;
    });
    
    html += `
                    <tr class="table-light">
                        <th>إجمالي الأصول</th>
                        <th class="text-end">${formatCurrency(data.assets.total)}</th>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>الخصوم وحقوق الملكية</h5>
                <table class="table table-sm">
    `;
    
    data.liabilities.accounts.forEach(account => {
        html += `
            <tr>
                <td>${account.name}</td>
                <td class="text-end">${formatCurrency(account.balance)}</td>
            </tr>
        `;
    });
    
    data.equity.accounts.forEach(account => {
        html += `
            <tr>
                <td>${account.name}</td>
                <td class="text-end">${formatCurrency(account.balance)}</td>
            </tr>
        `;
    });
    
    html += `
                    <tr class="table-light">
                        <th>إجمالي الخصوم وحقوق الملكية</th>
                        <th class="text-end">${formatCurrency(data.total_liabilities_equity)}</th>
                    </tr>
                </table>
            </div>
        </div>
    `;
    
    return html;
}

// إنشاء HTML لقائمة الدخل
function generateIncomeStatementHTML(data) {
    let html = `
        <div class="text-center mb-3">
            <h4>قائمة الدخل</h4>
            <p>للفترة من ${formatDate(data.date_from)} إلى ${formatDate(data.date_to)}</p>
        </div>
        <div class="table-responsive">
            <table class="table">
                <tr>
                    <th colspan="2">الإيرادات</th>
                </tr>
    `;
    
    data.revenue.accounts.forEach(account => {
        html += `
            <tr>
                <td class="ps-3">${account.name}</td>
                <td class="text-end">${formatCurrency(account.balance)}</td>
            </tr>
        `;
    });
    
    html += `
                <tr class="table-light">
                    <th>إجمالي الإيرادات</th>
                    <th class="text-end">${formatCurrency(data.revenue.total)}</th>
                </tr>
                <tr>
                    <th colspan="2">المصروفات</th>
                </tr>
    `;
    
    data.expenses.accounts.forEach(account => {
        html += `
            <tr>
                <td class="ps-3">${account.name}</td>
                <td class="text-end">${formatCurrency(account.balance)}</td>
            </tr>
        `;
    });
    
    html += `
                <tr class="table-light">
                    <th>إجمالي المصروفات</th>
                    <th class="text-end">${formatCurrency(data.expenses.total)}</th>
                </tr>
                <tr class="table-primary">
                    <th>صافي ${data.net_income >= 0 ? 'الربح' : 'الخسارة'}</th>
                    <th class="text-end">${formatCurrency(Math.abs(data.net_income))}</th>
                </tr>
            </table>
        </div>
    `;
    
    return html;
}

// إنشاء HTML لأعمار ديون العملاء
function generateCustomerAgingHTML(data) {
    let html = `
        <div class="text-center mb-3">
            <h4>أعمار ديون العملاء</h4>
            <p>كما في ${formatDate(data.as_of_date)}</p>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>العميل</th>
                        <th>غير مستحق</th>
                        <th>1-30 يوم</th>
                        <th>31-60 يوم</th>
                        <th>61-90 يوم</th>
                        <th>أكثر من 90 يوم</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.customers.forEach(customer => {
        html += `
            <tr>
                <td>${customer.customer_name}</td>
                <td class="text-end">${formatCurrency(customer.current)}</td>
                <td class="text-end">${formatCurrency(customer.days_1_30)}</td>
                <td class="text-end">${formatCurrency(customer.days_31_60)}</td>
                <td class="text-end">${formatCurrency(customer.days_61_90)}</td>
                <td class="text-end">${formatCurrency(customer.over_90_days)}</td>
                <td class="text-end"><strong>${formatCurrency(customer.total_balance)}</strong></td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// طباعة التقرير
function printReport() {
    const reportContent = document.getElementById('reportContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة التقرير</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { direction: rtl; font-family: Arial, sans-serif; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                ${reportContent}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تصدير التقرير
function exportReport() {
    // يمكن تطوير هذه الوظيفة لتصدير التقرير إلى Excel أو PDF
    alert('سيتم تطوير وظيفة التصدير قريباً');
}

// تعيين التاريخ الافتراضي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reportDateTo').value = today;
});
</script>
{% endblock %}
