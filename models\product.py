from . import db
from datetime import datetime

class ProductCategory(db.Model):
    """نموذج فئات المنتجات"""
    __tablename__ = 'product_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description = db.Column(db.Text)
    is_active = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    products = db.relationship('Product', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<ProductCategory {self.code}: {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Product(db.Model):
    """نموذج المنتجات"""
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'))
    unit = db.Column(db.String(20), default='قطعة')  # وحدة القياس
    cost_price = db.Column(db.Numeric(15, 2), default=0)  # سعر التكلفة
    selling_price = db.Column(db.Numeric(15, 2), default=0)  # سعر البيع
    min_stock = db.Column(db.Numeric(10, 2), default=0)  # الحد الأدنى للمخزون
    max_stock = db.Column(db.Numeric(10, 2), default=0)  # الحد الأقصى للمخزون
    current_stock = db.Column(db.Numeric(10, 2), default=0)  # المخزون الحالي
    barcode = db.Column(db.String(50), unique=True)  # الباركود
    is_active = db.Column(db.Boolean, default=True)
    is_service = db.Column(db.Boolean, default=False)  # هل هو خدمة أم منتج
    tax_rate = db.Column(db.Numeric(5, 2), default=0)  # معدل الضريبة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    invoice_items = db.relationship('InvoiceItem', backref='product', lazy='dynamic')
    
    def __repr__(self):
        return f'<Product {self.code}: {self.name}>'
    
    @property
    def stock_value(self):
        """قيمة المخزون"""
        return float(self.current_stock * self.cost_price) if self.current_stock and self.cost_price else 0
    
    @property
    def is_low_stock(self):
        """هل المخزون أقل من الحد الأدنى"""
        return self.current_stock < self.min_stock if self.min_stock else False
    
    @property
    def profit_margin(self):
        """هامش الربح"""
        if self.cost_price and self.selling_price:
            return float((self.selling_price - self.cost_price) / self.selling_price * 100)
        return 0
    
    def to_dict(self):
        """تحويل المنتج إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'description': self.description,
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'unit': self.unit,
            'cost_price': float(self.cost_price) if self.cost_price else 0,
            'selling_price': float(self.selling_price) if self.selling_price else 0,
            'min_stock': float(self.min_stock) if self.min_stock else 0,
            'max_stock': float(self.max_stock) if self.max_stock else 0,
            'current_stock': float(self.current_stock) if self.current_stock else 0,
            'barcode': self.barcode,
            'is_active': self.is_active,
            'is_service': self.is_service,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'stock_value': self.stock_value,
            'is_low_stock': self.is_low_stock,
            'profit_margin': self.profit_margin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
