from flask import Blueprint, request, jsonify
from models import db, Invoice, InvoiceItem, Customer, Product
from sqlalchemy import or_, desc
from datetime import datetime, timedelta

invoices_bp = Blueprint('invoices', __name__)

@invoices_bp.route('/', methods=['GET'])
def get_invoices():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        customer_id = request.args.get('customer_id', type=int)
        posted_only = request.args.get('posted_only', 'false').lower() == 'true'
        
        query = Invoice.query
        
        if search:
            query = query.filter(or_(
                Invoice.invoice_number.contains(search),
                Invoice.notes.contains(search)
            ))
        
        if customer_id:
            query = query.filter(Invoice.customer_id == customer_id)
        
        if posted_only:
            query = query.filter(Invoice.is_posted == True)
        
        query = query.order_by(desc(Invoice.invoice_date), desc(Invoice.id))
        invoices = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'invoices': [invoice.to_dict() for invoice in invoices.items],
            'total': invoices.total,
            'pages': invoices.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@invoices_bp.route('/', methods=['POST'])
def create_invoice():
    try:
        data = request.get_json()
        
        required_fields = ['customer_id', 'items']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        if not data['items']:
            return jsonify({'error': 'يجب أن تحتوي الفاتورة على بند واحد على الأقل'}), 400
        
        # التحقق من وجود العميل
        customer = Customer.query.get(data['customer_id'])
        if not customer:
            return jsonify({'error': 'العميل غير موجود'}), 400
        
        # إنشاء رقم الفاتورة
        invoice_number = data.get('invoice_number')
        if not invoice_number:
            last_invoice = Invoice.query.order_by(desc(Invoice.id)).first()
            next_number = (last_invoice.id + 1) if last_invoice else 1
            invoice_number = f"INV{next_number:06d}"
        
        # تحويل التواريخ
        invoice_date = datetime.now().date()
        if 'invoice_date' in data and data['invoice_date']:
            invoice_date = datetime.strptime(data['invoice_date'], '%Y-%m-%d').date()
        
        due_date = invoice_date + timedelta(days=customer.payment_terms)
        if 'due_date' in data and data['due_date']:
            due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
        
        # إنشاء الفاتورة
        invoice = Invoice(
            invoice_number=invoice_number,
            invoice_date=invoice_date,
            due_date=due_date,
            customer_id=data['customer_id'],
            payment_terms=data.get('payment_terms', customer.payment_terms),
            discount_amount=data.get('discount_amount', 0),
            notes=data.get('notes'),
            created_by=data.get('created_by', 'النظام')
        )
        
        db.session.add(invoice)
        db.session.flush()
        
        # إنشاء بنود الفاتورة
        for item_data in data['items']:
            if 'description' not in item_data or 'quantity' not in item_data or 'unit_price' not in item_data:
                db.session.rollback()
                return jsonify({'error': 'الوصف والكمية وسعر الوحدة مطلوبة لكل بند'}), 400
            
            item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=item_data.get('product_id'),
                description=item_data['description'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
                discount_rate=item_data.get('discount_rate', 0),
                tax_rate=item_data.get('tax_rate', 0)
            )
            db.session.add(item)
        
        # حساب المجاميع
        invoice.calculate_totals()
        
        db.session.commit()
        return jsonify(invoice.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoices_bp.route('/<int:invoice_id>', methods=['GET'])
def get_invoice(invoice_id):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        return jsonify(invoice.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@invoices_bp.route('/<int:invoice_id>', methods=['PUT'])
def update_invoice(invoice_id):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        if invoice.is_posted:
            return jsonify({'error': 'لا يمكن تعديل فاتورة مرحلة'}), 400
        
        data = request.get_json()
        
        # تحديث البيانات الأساسية
        for field in ['notes', 'discount_amount']:
            if field in data:
                setattr(invoice, field, data[field])
        
        if 'invoice_date' in data and data['invoice_date']:
            invoice.invoice_date = datetime.strptime(data['invoice_date'], '%Y-%m-%d').date()
        
        if 'due_date' in data and data['due_date']:
            invoice.due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
        
        # تحديث البنود إذا تم تمريرها
        if 'items' in data:
            # حذف البنود القديمة
            InvoiceItem.query.filter_by(invoice_id=invoice_id).delete()
            
            # إنشاء البنود الجديدة
            for item_data in data['items']:
                item = InvoiceItem(
                    invoice_id=invoice_id,
                    product_id=item_data.get('product_id'),
                    description=item_data['description'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    discount_rate=item_data.get('discount_rate', 0),
                    tax_rate=item_data.get('tax_rate', 0)
                )
                db.session.add(item)
            
            # إعادة حساب المجاميع
            invoice.calculate_totals()
        
        db.session.commit()
        return jsonify(invoice.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoices_bp.route('/<int:invoice_id>/post', methods=['POST'])
def post_invoice(invoice_id):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        if invoice.post():
            db.session.commit()
            return jsonify({'message': 'تم ترحيل الفاتورة بنجاح'})
        else:
            return jsonify({'error': 'لا يمكن ترحيل الفاتورة'}), 400
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoices_bp.route('/<int:invoice_id>', methods=['DELETE'])
def delete_invoice(invoice_id):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        if invoice.is_posted:
            return jsonify({'error': 'لا يمكن حذف فاتورة مرحلة'}), 400
        
        db.session.delete(invoice)
        db.session.commit()
        return jsonify({'message': 'تم حذف الفاتورة بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
