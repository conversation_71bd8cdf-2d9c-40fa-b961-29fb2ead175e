{% extends "base.html" %}

{% block title %}دليل الحسابات - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-journal-text"></i> دليل الحسابات</h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#accountModal">
        <i class="bi bi-plus-circle"></i> حساب جديد
    </button>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الحسابات...">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="accountTypeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="ASSET">الأصول</option>
                    <option value="LIABILITY">الخصوم</option>
                    <option value="EQUITY">حقوق الملكية</option>
                    <option value="REVENUE">الإيرادات</option>
                    <option value="EXPENSE">المصروفات</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="activeOnlyFilter" checked>
                    <label class="form-check-label" for="activeOnlyFilter">
                        النشطة فقط
                    </label>
                </div>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="loadAccounts()">
                    <i class="bi bi-arrow-clockwise"></i> تحديث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- جدول الحسابات -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="accountsTable">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>اسم الحساب</th>
                        <th>النوع</th>
                        <th>الرصيد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="accountsTableBody">
                    <tr>
                        <td colspan="6" class="text-center">جاري التحميل...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- التصفح -->
        <div id="paginationContainer"></div>
    </div>
</div>

<!-- مودال إضافة/تعديل حساب -->
<div class="modal fade" id="accountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="accountModalTitle">حساب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountForm">
                <div class="modal-body">
                    <input type="hidden" id="accountId" name="id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="accountCode" class="form-label">كود الحساب *</label>
                            <input type="text" class="form-control" id="accountCode" name="code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="accountType" class="form-label">نوع الحساب *</label>
                            <select class="form-select" id="accountType" name="account_type" required>
                                <option value="">اختر النوع</option>
                                <option value="ASSET">الأصول</option>
                                <option value="LIABILITY">الخصوم</option>
                                <option value="EQUITY">حقوق الملكية</option>
                                <option value="REVENUE">الإيرادات</option>
                                <option value="EXPENSE">المصروفات</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="accountName" class="form-label">اسم الحساب *</label>
                            <input type="text" class="form-control" id="accountName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="accountNameEn" class="form-label">الاسم بالإنجليزية</label>
                            <input type="text" class="form-control" id="accountNameEn" name="name_en">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parentAccount" class="form-label">الحساب الأب</label>
                            <select class="form-select" id="parentAccount" name="parent_id">
                                <option value="">لا يوجد</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="accountDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="accountDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="saveSpinner"></span>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let accountsData = [];

// تحميل الحسابات
async function loadAccounts(page = 1) {
    try {
        showLoading('#accountsTableBody');
        
        const params = new URLSearchParams({
            page: page,
            per_page: 25,
            search: document.getElementById('searchInput').value,
            type: document.getElementById('accountTypeFilter').value,
            active_only: document.getElementById('activeOnlyFilter').checked
        });
        
        const data = await apiRequest(`/accounts?${params}`);
        accountsData = data.accounts;
        currentPage = page;
        
        updateAccountsTable(data.accounts);
        setupPagination('#paginationContainer', data.pages, data.current_page, loadAccounts);
        
    } catch (error) {
        showError('#accountsTableBody', error.message);
    }
}

function updateAccountsTable(accounts) {
    const tbody = document.getElementById('accountsTableBody');
    
    if (accounts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد حسابات</td></tr>';
        return;
    }
    
    tbody.innerHTML = accounts.map(account => `
        <tr>
            <td><strong>${account.code}</strong></td>
            <td>${account.name}</td>
            <td><span class="badge bg-secondary">${account.account_type}</span></td>
            <td class="text-currency">${formatCurrency(account.balance)}</td>
            <td>
                <span class="badge ${account.is_active ? 'bg-success' : 'bg-danger'}">
                    ${account.is_active ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editAccount(${account.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="viewAccountBalance(${account.id})">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteAccount(${account.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// إضافة/تعديل حساب
document.getElementById('accountForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    const accountId = data.id;
    
    // تحويل checkbox إلى boolean
    data.is_active = document.getElementById('isActive').checked;
    
    try {
        document.getElementById('saveSpinner').classList.remove('d-none');
        
        if (accountId) {
            await apiRequest(`/accounts/${accountId}`, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
            showSuccess('تم تحديث الحساب بنجاح');
        } else {
            await apiRequest('/accounts', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            showSuccess('تم إضافة الحساب بنجاح');
        }
        
        hideModal('#accountModal');
        resetForm('#accountForm');
        loadAccounts(currentPage);
        
    } catch (error) {
        showErrorMessage(error.message);
    } finally {
        document.getElementById('saveSpinner').classList.add('d-none');
    }
});

// تعديل حساب
async function editAccount(accountId) {
    try {
        const account = await apiRequest(`/accounts/${accountId}`);
        
        document.getElementById('accountModalTitle').textContent = 'تعديل الحساب';
        document.getElementById('accountId').value = account.id;
        document.getElementById('accountCode').value = account.code;
        document.getElementById('accountName').value = account.name;
        document.getElementById('accountNameEn').value = account.name_en || '';
        document.getElementById('accountType').value = account.account_type;
        document.getElementById('parentAccount').value = account.parent_id || '';
        document.getElementById('isActive').checked = account.is_active;
        document.getElementById('accountDescription').value = account.description || '';
        
        showModal('#accountModal');
        
    } catch (error) {
        showErrorMessage(error.message);
    }
}

// حذف حساب
async function deleteAccount(accountId) {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
        return;
    }
    
    try {
        await apiRequest(`/accounts/${accountId}`, { method: 'DELETE' });
        showSuccess('تم حذف الحساب بنجاح');
        loadAccounts(currentPage);
    } catch (error) {
        showErrorMessage(error.message);
    }
}

// عرض رصيد الحساب
async function viewAccountBalance(accountId) {
    try {
        const balance = await apiRequest(`/accounts/${accountId}/balance`);
        alert(`رصيد الحساب: ${formatCurrency(balance.balance)}`);
    } catch (error) {
        showErrorMessage(error.message);
    }
}

// إعداد البحث
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        loadAccounts(1);
    }, 500);
});

document.getElementById('accountTypeFilter').addEventListener('change', () => loadAccounts(1));
document.getElementById('activeOnlyFilter').addEventListener('change', () => loadAccounts(1));

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAccounts();
    
    // إعادة تعيين النموذج عند إغلاق المودال
    document.getElementById('accountModal').addEventListener('hidden.bs.modal', function() {
        resetForm('#accountForm');
        document.getElementById('accountModalTitle').textContent = 'حساب جديد';
        document.getElementById('accountId').value = '';
    });
});
</script>
{% endblock %}
