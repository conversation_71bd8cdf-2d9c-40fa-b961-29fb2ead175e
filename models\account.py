from . import db
from datetime import datetime
from enum import Enum

class AccountType(Enum):
    """أنواع الحسابات المحاسبية"""
    ASSET = "أصول"           # Assets
    LIABILITY = "خصوم"       # Liabilities  
    EQUITY = "حقوق الملكية"   # Equity
    REVENUE = "إيرادات"      # Revenue
    EXPENSE = "مصروفات"      # Expenses

class Account(db.Model):
    """نموذج الحسابات المحاسبية"""
    __tablename__ = 'accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))  # الاسم بالإنجليزية
    account_type = db.Column(db.Enum(AccountType), nullable=False)
    parent_id = db.Column(db.Integer, db.<PERSON>Key('accounts.id'))
    is_active = db.Column(db.<PERSON>, default=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    parent = db.relationship('Account', remote_side=[id], backref='children')
    transaction_entries = db.relationship('TransactionEntry', backref='account', lazy='dynamic')
    
    def __repr__(self):
        return f'<Account {self.code}: {self.name}>'
    
    @property
    def balance(self):
        """حساب رصيد الحساب"""
        debit_total = sum(entry.debit_amount for entry in self.transaction_entries if entry.debit_amount)
        credit_total = sum(entry.credit_amount for entry in self.transaction_entries if entry.credit_amount)
        
        # حساب الرصيد حسب نوع الحساب
        if self.account_type in [AccountType.ASSET, AccountType.EXPENSE]:
            return debit_total - credit_total
        else:  # LIABILITY, EQUITY, REVENUE
            return credit_total - debit_total
    
    @property
    def full_code(self):
        """الكود الكامل للحساب مع الحساب الأب"""
        if self.parent:
            return f"{self.parent.full_code}.{self.code}"
        return self.code
    
    @property
    def level(self):
        """مستوى الحساب في الشجرة"""
        if self.parent:
            return self.parent.level + 1
        return 0
    
    def to_dict(self):
        """تحويل الحساب إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'account_type': self.account_type.value,
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'description': self.description,
            'balance': self.balance,
            'full_code': self.full_code,
            'level': self.level,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
