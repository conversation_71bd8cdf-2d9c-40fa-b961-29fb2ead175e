from flask import Blueprint, request, jsonify
from models import db, Account, AccountType
from sqlalchemy import or_

accounts_bp = Blueprint('accounts', __name__)

@accounts_bp.route('/', methods=['GET'])
def get_accounts():
    """الحصول على قائمة الحسابات"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        account_type = request.args.get('type', '')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = Account.query
        
        # تطبيق الفلاتر
        if search:
            query = query.filter(or_(
                Account.name.contains(search),
                Account.code.contains(search),
                Account.name_en.contains(search)
            ))
        
        if account_type:
            try:
                account_type_enum = AccountType(account_type)
                query = query.filter(Account.account_type == account_type_enum)
            except ValueError:
                pass
        
        if active_only:
            query = query.filter(Account.is_active == True)
        
        # ترتيب النتائج
        query = query.order_by(Account.code)
        
        # تطبيق التصفح
        accounts = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'accounts': [account.to_dict() for account in accounts.items],
            'total': accounts.total,
            'pages': accounts.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/<int:account_id>', methods=['GET'])
def get_account(account_id):
    """الحصول على حساب محدد"""
    try:
        account = Account.query.get_or_404(account_id)
        return jsonify(account.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/', methods=['POST'])
def create_account():
    """إنشاء حساب جديد"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['code', 'name', 'account_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        # التحقق من عدم تكرار الكود
        if Account.query.filter_by(code=data['code']).first():
            return jsonify({'error': 'كود الحساب موجود مسبقاً'}), 400
        
        # التحقق من صحة نوع الحساب
        try:
            account_type = AccountType(data['account_type'])
        except ValueError:
            return jsonify({'error': 'نوع الحساب غير صحيح'}), 400
        
        # إنشاء الحساب الجديد
        account = Account(
            code=data['code'],
            name=data['name'],
            name_en=data.get('name_en'),
            account_type=account_type,
            parent_id=data.get('parent_id'),
            description=data.get('description'),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(account)
        db.session.commit()
        
        return jsonify(account.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/<int:account_id>', methods=['PUT'])
def update_account(account_id):
    """تحديث حساب موجود"""
    try:
        account = Account.query.get_or_404(account_id)
        data = request.get_json()
        
        # التحقق من عدم تكرار الكود
        if 'code' in data and data['code'] != account.code:
            if Account.query.filter_by(code=data['code']).first():
                return jsonify({'error': 'كود الحساب موجود مسبقاً'}), 400
        
        # تحديث البيانات
        if 'code' in data:
            account.code = data['code']
        if 'name' in data:
            account.name = data['name']
        if 'name_en' in data:
            account.name_en = data['name_en']
        if 'account_type' in data:
            try:
                account.account_type = AccountType(data['account_type'])
            except ValueError:
                return jsonify({'error': 'نوع الحساب غير صحيح'}), 400
        if 'parent_id' in data:
            account.parent_id = data['parent_id']
        if 'description' in data:
            account.description = data['description']
        if 'is_active' in data:
            account.is_active = data['is_active']
        
        db.session.commit()
        return jsonify(account.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/<int:account_id>', methods=['DELETE'])
def delete_account(account_id):
    """حذف حساب"""
    try:
        account = Account.query.get_or_404(account_id)
        
        # التحقق من وجود معاملات مرتبطة
        if account.transaction_entries.count() > 0:
            return jsonify({'error': 'لا يمكن حذف الحساب لوجود معاملات مرتبطة به'}), 400
        
        # التحقق من وجود حسابات فرعية
        if account.children:
            return jsonify({'error': 'لا يمكن حذف الحساب لوجود حسابات فرعية'}), 400
        
        db.session.delete(account)
        db.session.commit()
        
        return jsonify({'message': 'تم حذف الحساب بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/tree', methods=['GET'])
def get_accounts_tree():
    """الحصول على شجرة الحسابات"""
    try:
        # الحصول على الحسابات الرئيسية فقط
        root_accounts = Account.query.filter_by(parent_id=None, is_active=True).order_by(Account.code).all()
        
        def build_tree(account):
            """بناء شجرة الحسابات"""
            account_dict = account.to_dict()
            children = Account.query.filter_by(parent_id=account.id, is_active=True).order_by(Account.code).all()
            if children:
                account_dict['children'] = [build_tree(child) for child in children]
            return account_dict
        
        tree = [build_tree(account) for account in root_accounts]
        
        return jsonify(tree)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/types', methods=['GET'])
def get_account_types():
    """الحصول على أنواع الحسابات"""
    try:
        types = [{'value': account_type.name, 'label': account_type.value} for account_type in AccountType]
        return jsonify(types)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@accounts_bp.route('/<int:account_id>/balance', methods=['GET'])
def get_account_balance(account_id):
    """الحصول على رصيد حساب محدد"""
    try:
        account = Account.query.get_or_404(account_id)
        return jsonify({
            'account_id': account_id,
            'account_name': account.name,
            'account_code': account.code,
            'balance': float(account.balance),
            'account_type': account.account_type.value
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
