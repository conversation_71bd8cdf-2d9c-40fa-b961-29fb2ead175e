from flask import Blueprint, request, jsonify
from models import db, Employee
from sqlalchemy import or_
from datetime import datetime

employees_bp = Blueprint('employees', __name__)

@employees_bp.route('/', methods=['GET'])
def get_employees():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = Employee.query
        
        if search:
            query = query.filter(or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.employee_number.contains(search),
                Employee.email.contains(search)
            ))
        
        if active_only:
            query = query.filter(Employee.is_active == True)
        
        query = query.order_by(Employee.employee_number)
        employees = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'employees': [employee.to_dict() for employee in employees.items],
            'total': employees.total,
            'pages': employees.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/', methods=['POST'])
def create_employee():
    try:
        data = request.get_json()
        
        required_fields = ['employee_number', 'first_name', 'last_name', 'hire_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        if Employee.query.filter_by(employee_number=data['employee_number']).first():
            return jsonify({'error': 'رقم الموظف موجود مسبقاً'}), 400
        
        # تحويل التواريخ
        if 'hire_date' in data:
            data['hire_date'] = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
        if 'birth_date' in data and data['birth_date']:
            data['birth_date'] = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
        if 'termination_date' in data and data['termination_date']:
            data['termination_date'] = datetime.strptime(data['termination_date'], '%Y-%m-%d').date()
        
        employee = Employee(**{k: v for k, v in data.items() if hasattr(Employee, k)})
        db.session.add(employee)
        db.session.commit()
        
        return jsonify(employee.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['GET'])
def get_employee(employee_id):
    try:
        employee = Employee.query.get_or_404(employee_id)
        return jsonify(employee.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['PUT'])
def update_employee(employee_id):
    try:
        employee = Employee.query.get_or_404(employee_id)
        data = request.get_json()
        
        # تحويل التواريخ
        if 'hire_date' in data and data['hire_date']:
            data['hire_date'] = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
        if 'birth_date' in data and data['birth_date']:
            data['birth_date'] = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
        if 'termination_date' in data and data['termination_date']:
            data['termination_date'] = datetime.strptime(data['termination_date'], '%Y-%m-%d').date()
        
        for field in data:
            if hasattr(employee, field):
                setattr(employee, field, data[field])
        
        db.session.commit()
        return jsonify(employee.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    try:
        employee = Employee.query.get_or_404(employee_id)
        db.session.delete(employee)
        db.session.commit()
        return jsonify({'message': 'تم حذف الموظف بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
