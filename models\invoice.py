from . import db
from datetime import datetime, timedelta

class Invoice(db.Model):
    """نموذج الفواتير"""
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    due_date = db.Column(db.Date)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    subtotal = db.Column(db.Numeric(15, 2), default=0)  # المجموع الفرعي
    tax_amount = db.Column(db.Numeric(15, 2), default=0)  # مبلغ الضريبة
    discount_amount = db.Column(db.Numeric(15, 2), default=0)  # مبلغ الخصم
    total_amount = db.Column(db.Numeric(15, 2), default=0)  # المجموع الكلي
    paid_amount = db.Column(db.Numeric(15, 2), default=0)  # المبلغ المدفوع
    currency = db.Column(db.String(3), default='SAR')  # العملة
    exchange_rate = db.Column(db.Numeric(10, 4), default=1)  # سعر الصرف
    payment_terms = db.Column(db.Integer, default=30)  # شروط الدفع بالأيام
    notes = db.Column(db.Text)
    is_posted = db.Column(db.Boolean, default=False)  # هل تم ترحيل الفاتورة
    is_paid = db.Column(db.Boolean, default=False)  # هل تم دفع الفاتورة
    created_by = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    posted_at = db.Column(db.DateTime)
    
    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}: {self.total_amount}>'
    
    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return float(self.total_amount - self.paid_amount) if self.total_amount and self.paid_amount else float(self.total_amount or 0)
    
    @property
    def is_overdue(self):
        """هل الفاتورة متأخرة"""
        if self.due_date and not self.is_paid:
            return datetime.utcnow().date() > self.due_date
        return False
    
    @property
    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue:
            return (datetime.utcnow().date() - self.due_date).days
        return 0
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.subtotal = sum(item.total_amount for item in self.items)
        self.tax_amount = sum(item.tax_amount for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
    
    def post(self):
        """ترحيل الفاتورة"""
        if not self.is_posted:
            self.is_posted = True
            self.posted_at = datetime.utcnow()
            # TODO: إنشاء قيد محاسبي للفاتورة
            return True
        return False
    
    def to_dict(self):
        """تحويل الفاتورة إلى قاموس"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'invoice_date': self.invoice_date.isoformat() if self.invoice_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'customer_id': self.customer_id,
            'customer_name': self.customer.name if self.customer else None,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'remaining_amount': self.remaining_amount,
            'currency': self.currency,
            'exchange_rate': float(self.exchange_rate) if self.exchange_rate else 1,
            'payment_terms': self.payment_terms,
            'notes': self.notes,
            'is_posted': self.is_posted,
            'is_paid': self.is_paid,
            'is_overdue': self.is_overdue,
            'days_overdue': self.days_overdue,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'posted_at': self.posted_at.isoformat() if self.posted_at else None,
            'items': [item.to_dict() for item in self.items]
        }

class InvoiceItem(db.Model):
    """نموذج بنود الفاتورة"""
    __tablename__ = 'invoice_items'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    description = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    discount_rate = db.Column(db.Numeric(5, 2), default=0)  # معدل الخصم
    tax_rate = db.Column(db.Numeric(5, 2), default=0)  # معدل الضريبة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<InvoiceItem {self.description}: {self.quantity} x {self.unit_price}>'
    
    @property
    def line_total(self):
        """إجمالي السطر قبل الخصم والضريبة"""
        return float(self.quantity * self.unit_price) if self.quantity and self.unit_price else 0
    
    @property
    def discount_amount(self):
        """مبلغ الخصم"""
        return float(self.line_total * self.discount_rate / 100) if self.discount_rate else 0
    
    @property
    def net_amount(self):
        """المبلغ الصافي بعد الخصم"""
        return self.line_total - self.discount_amount
    
    @property
    def tax_amount(self):
        """مبلغ الضريبة"""
        return float(self.net_amount * self.tax_rate / 100) if self.tax_rate else 0
    
    @property
    def total_amount(self):
        """إجمالي السطر بعد الخصم والضريبة"""
        return self.net_amount + self.tax_amount
    
    def to_dict(self):
        """تحويل بند الفاتورة إلى قاموس"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'product_code': self.product.code if self.product else None,
            'description': self.description,
            'quantity': float(self.quantity) if self.quantity else 0,
            'unit_price': float(self.unit_price) if self.unit_price else 0,
            'discount_rate': float(self.discount_rate) if self.discount_rate else 0,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'line_total': self.line_total,
            'discount_amount': self.discount_amount,
            'net_amount': self.net_amount,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
